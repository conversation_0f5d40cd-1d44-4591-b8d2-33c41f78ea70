{"format": 1, "restore": {"D:\\ASPNETMONGO\\AspnetMongo\\AspnetMongo.csproj": {}}, "projects": {"D:\\ASPNETMONGO\\AspnetMongo\\AspnetMongo.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\ASPNETMONGO\\AspnetMongo\\AspnetMongo.csproj", "projectName": "AspnetMongo", "projectPath": "D:\\ASPNETMONGO\\AspnetMongo\\AspnetMongo.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\ASPNETMONGO\\AspnetMongo\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": {"target": "Package", "version": "[9.0.9, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.9, )"}, "MongoDB.Driver": {"target": "Package", "version": "[3.5.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.6.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305/PortableRuntimeIdentifierGraph.json"}}}}}