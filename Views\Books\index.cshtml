@model IEnumerable<AspNetMongo.Models.Book>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>Daftar Buku</title>
    <style>
        body { font-family: Arial, sans-serif; background-color: #fafafa; padding: 20px; }
        h1 { color: #333; text-align: center; }
        table { width: 80%; margin: 20px auto; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #4CAF50; color: white; }
        tr:nth-child(even) { background-color: #f2f2f2; }
        tr:hover { background-color: #ddd; }
        .add-button {
            display: block;
            width: 150px;
            margin: 20px auto;
            padding: 10px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>📚 Daftar Buku</h1>

    <a class="add-button" href="/api/books">Tambah Buku (via API)</a>

    <table>
        <tr>
            <th>Judul</th>
            <th><PERSON><PERSON>s</th>
            <th><PERSON><PERSON><PERSON></th>
            <th>Harga</th>
        </tr>

        @foreach (var book in Model)
        {
            <tr>
                <td>@book.Title</td>
                <td>@book.Author</td>
                <td>@book.Category</td>
                <td>@book.Price</td>
            </tr>
        }
    </table>
</body>
</html>
