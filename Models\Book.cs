using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace AspNetMongo.Models
{
    public class Book
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string? Id { get; set; }

        [BsonElement("Title")]
        public string Title { get; set; } = null!;

        [BsonElement("Author")]
        public string Author { get; set; } = null!;

        [BsonElement("Price")]
        public int Price { get; set; }

        [BsonElement("Category")]
        public string Category { get; set; } = null!;
    }
}
