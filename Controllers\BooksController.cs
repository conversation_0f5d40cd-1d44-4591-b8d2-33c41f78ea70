using AspNetMongo.Models;
using AspNetMongo.Services;
using Microsoft.AspNetCore.Mvc;

namespace AspNetMongo.Controllers
{
    public class BooksController : Controller
    {
        private readonly BookService _bookService;

        public BooksController(BookService bookService) =>
            _bookService = bookService;

        public async Task<IActionResult> Index()
        {
            var books = await _bookService.GetAsync();
            return View(books);
        }
    }
}
